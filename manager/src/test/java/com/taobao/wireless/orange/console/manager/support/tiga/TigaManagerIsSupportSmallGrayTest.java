package com.taobao.wireless.orange.console.manager.support.tiga;

import com.taobao.wireless.orange.console.BaseManagerDebug;
import com.taobao.wireless.orange.core.service.model.NamespaceVersionBO;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.HashMap;

import static com.taobao.wireless.orange.console.manager.BaseMockUnitManager.TEST_APP_KEY;
import static com.taobao.wireless.orange.console.manager.config.switcher.SwitchConfig.APP_KEY_2_SMALL_GRAY_MIN_APP_VERSION_MAP;
import static org.junit.Assert.*;

/**
 * TigaManager.isSupportSmallGray方法的简化单元测试
 *
 * 这个测试类使用实际的Spring上下文，避免了复杂的Mock设置
 * 主要测试各种业务场景下的返回值是否正确
 */
public class TigaManagerIsSupportSmallGrayTest extends BaseManagerDebug {

    @Autowired
    private TigaManager tigaManager;

    private static final String MIN_APP_VERSION = "10.0.0";

    // 保存原始配置，用于测试后恢复
    private HashMap<String, String> originalConfig;

    @Before
    public void setUp() {
        // 备份原始配置
        originalConfig = new HashMap<>(APP_KEY_2_SMALL_GRAY_MIN_APP_VERSION_MAP);

        // 设置测试用的最小版本配置
        APP_KEY_2_SMALL_GRAY_MIN_APP_VERSION_MAP.put(TEST_APP_KEY, MIN_APP_VERSION);
    }

    @After
    public void tearDown() {
        // 恢复原始配置
        APP_KEY_2_SMALL_GRAY_MIN_APP_VERSION_MAP.clear();
        APP_KEY_2_SMALL_GRAY_MIN_APP_VERSION_MAP.putAll(originalConfig);
    }
    
    /**
     * 创建NamespaceVersionBO对象的辅助方法
     */
    private NamespaceVersionBO createVersionBO(String appKey, String strategy) {
        NamespaceVersionBO versionBO = new NamespaceVersionBO();
        versionBO.setAppKey(appKey);
        versionBO.setStrategy(strategy);
        return versionBO;
    }
}
