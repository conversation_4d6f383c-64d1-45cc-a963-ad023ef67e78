package com.taobao.wireless.orange.console.manager.support.tiga;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.schedulerx.shade.com.google.common.collect.ImmutableMap;
import com.taobao.wireless.orange.console.manager.expression.Fields;
import com.taobao.wireless.orange.console.manager.expression.OpExpr;
import com.taobao.wireless.orange.console.manager.expression.StrategyExpression;
import com.taobao.wireless.orange.console.manager.manager.namespace.NamespaceChangeManager;
import com.taobao.wireless.orange.console.manager.support.MassGraySupport;
import com.taobao.wireless.orange.console.manager.support.user.UserUtils;
import com.taobao.wireless.orange.console.manager.util.VersionUtil;
import com.taobao.wireless.orange.core.dao.NamespaceDAO;
import com.taobao.wireless.orange.core.dao.impl.NamespaceVersionDAOImpl;
import com.taobao.wireless.orange.core.dao.model.NamespaceDO;
import com.taobao.wireless.orange.core.dao.model.NamespaceVersionDO;
import com.taobao.wireless.orange.core.exception.CommonException;
import com.taobao.wireless.orange.core.exception.ExceptionEnum;
import com.taobao.wireless.orange.core.service.model.NamespaceChangeBO;
import com.taobao.wireless.orange.core.service.model.NamespaceVersionBO;
import com.taobao.wireless.orange.core.type.LoadLevel;
import com.taobao.wireless.orange.core.type.NamespaceType;
import com.taobao.wireless.tiga.release.console.api.task.model.param.TaskCreateParam;
import com.taobao.wireless.tiga.release.console.api.template.model.dto.TemplateInstanceDTO;
import com.taobao.wireless.tiga.release.expression.*;
import lombok.Builder;
import lombok.Data;
import lombok.NonNull;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.*;
import java.util.stream.Collectors;

import static com.taobao.wireless.orange.console.manager.config.switcher.SwitchConfig.APP_KEY_2_SMALL_GRAY_MIN_APP_VERSION_MAP;
import static com.taobao.wireless.orange.console.manager.support.MassGraySupport.ORANGE_IGNORE_DIMENSIONS_SET;

public class TigaManager {

    @Autowired
    private NamespaceDAO namespaceDAO;

    @Autowired
    private NamespaceVersionDAOImpl namespaceVersionDAO;

    @Autowired
    private NamespaceChangeManager namespaceChangeManager;

    @Autowired
    private TigaService tigaService;

    @Autowired
    private MassGraySupport massGraySupport;

    private static final Map<String, String> TIGA_DIMENSIONS_MAP = ImmutableMap.<String, String>builder()
            .put(Fields.OS_VER, Field.OS_VERSION.getValue())
            .put(Fields.M_BRAND, Field.BRAND.getValue())
            .put(Fields.M_MODEL, Field.MODEL.getValue())
            .put(Fields.APP_VER, Field.APP_VERSION.getValue())
            .build();

    private static final Map<String, String> TIGA_OP_MAP = ImmutableMap.<String, String>builder()
            .put("=", EqualityOperator.EQUALS.getValue())
            .build();

    public Long createTigaTask(String namespaceId, String version) {
        NamespaceVersionDO namespaceVersionDO = namespaceVersionDAO.selectByNamespaceIdAndVersion(namespaceId, version);
        if (namespaceVersionDO == null) {
            throw new CommonException(ExceptionEnum.PARAM_INVALID, "配置版本不存在");
        }

        TemplateInstanceDTO defaultTemplate = tigaService.getDefaultTemplateInstance(namespaceVersionDO.getAppKey(), TigaActionType.TEXT.getCode());
        if (defaultTemplate == null) {
            throw new CommonException(ExceptionEnum.PARAM_INVALID, "未找到默认模板");
        }
        return createTigaTask(namespaceVersionDO, defaultTemplate.getTemplateId());
    }

    private Long createTigaTask(@NonNull NamespaceVersionDO namespaceVersionDO, @NonNull Long templateId) {
        TaskCreateParam taskCreateParam = new TaskCreateParam();
        taskCreateParam.setTemplateId(templateId);
        taskCreateParam.setSourceOrderId(namespaceVersionDO.getVersion());
        taskCreateParam.setAppKeyList(Collections.singletonList(namespaceVersionDO.getAppKey()));
        taskCreateParam.setCfApplyId("mock_cf");
        taskCreateParam.setActionUrl("https://orange-console.alibaba-inc.com");

        NamespaceDO namespace = namespaceDAO.selectByNamespaceId(namespaceVersionDO.getNamespaceId());
        taskCreateParam.setName(String.format("【%s】配置变更", namespace.getName()));

        if (StringUtils.isNotBlank(namespace.getOwners())) {
            taskCreateParam.setDeveloperList(parseUserList(namespace.getOwners()));
        }

        if (StringUtils.isNotBlank(namespace.getTesters())) {
            taskCreateParam.setTesterList(parseUserList(namespace.getTesters()));
        }

        if (CollectionUtils.isEmpty(taskCreateParam.getDeveloperList())) {
            throw new CommonException(ExceptionEnum.PARAM_INVALID, "namespace 缺失有效管理员");
        }

        if (CollectionUtils.isEmpty(taskCreateParam.getTesterList())) {
            throw new CommonException(ExceptionEnum.PARAM_INVALID, "namespace 缺失有效测试负责人");
        }

        taskCreateParam.setPublishContent(generatePublishContent(namespaceVersionDO, namespace));

        TaskCreateParam.OrangeActionEntity orangeActionEntity = new TaskCreateParam.OrangeActionEntity();
        orangeActionEntity.setNamespace(namespace.getName());

        taskCreateParam.setActionEntity(orangeActionEntity);
        LogicExpression logicExpression = generateGrayCondition(namespaceVersionDO);
        taskCreateParam.setGrayCondition(logicExpression == null ? null : ExpressionParser.toStandardJson(logicExpression));
        taskCreateParam.setActionType(TigaActionType.TEXT.getCode());

        Long taskId = tigaService.createTask(taskCreateParam);

        TigaMetadata tigaMetadata = TigaMetadata.builder().taskId(taskId).templateId(templateId).build();
        NamespaceVersionDO updateObj = new NamespaceVersionDO();
        updateObj.setTigaMetadata(JSON.toJSONString(tigaMetadata));
        namespaceVersionDAO.updateByNamespaceIdAndVersion(namespaceVersionDO.getNamespaceId(), namespaceVersionDO.getVersion(), updateObj);

        return taskId;
    }

    private String generatePublishContent(NamespaceVersionDO namespaceVersionDO, NamespaceDO namespace) {
        NamespaceChangeBO changeBO = namespaceChangeManager.queryOneForNamespace(namespaceVersionDO.getNamespaceId(), namespaceVersionDO.getVersion(), null);

        LoadLevel loadLevel = LoadLevel.convert(namespace.getLoadLevel());
        GrayPublishContent publishContent = GrayPublishContent.builder()
                .name(namespace.getName())
                .type(NamespaceType.convert(namespace.getType()).name())
                .highLazy(loadLevel.getHighLazy())
                .loadLevel(loadLevel.getName())
                .appVersion(namespaceVersionDO.getAppVersion())
                .md5(namespaceVersionDO.getMd5())
                .resourceId(namespaceVersionDO.getResourceId())
                .version(namespaceVersionDO.getVersion())
                .changeVersion(changeBO.getChangeVersion())
                .build();

        return JSON.toJSONString(publishContent);
    }

    private LogicExpression generateGrayCondition(NamespaceVersionDO namespaceVersionDO) {
        String strategy = namespaceVersionDO.getStrategy();
        if (StringUtils.isBlank(strategy)) {
            // 如果是兜底策略的话需要将所有非空策略取反
            List<LogicExpression> children = massGraySupport.getAllNoEmptyStrategy(namespaceVersionDO.getAppKey(), namespaceVersionDO.getName(), namespaceVersionDO.getVersion()).stream()
                    .map(this::fromStrategy)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());

            return CollectionUtils.isEmpty(children) ? null :
                    new NotExpressionNode(new LogicalExpressionNode(LogicalOperator.OR, children));
        }

        return fromStrategy(strategy);
    }

    public boolean isSupportSmallGray(NamespaceVersionBO versionBO) {
        String appKey = versionBO.getAppKey();
        String minAppVersion = APP_KEY_2_SMALL_GRAY_MIN_APP_VERSION_MAP.get(appKey);
        // 未配置的应用则不支持小流量灰度
        if (StringUtils.isBlank(minAppVersion)) {
            return false;
        }

        String strategy = versionBO.getStrategy();

        // 1. 修改的是非兜底策略
        if (StringUtils.isNotBlank(strategy)) {
            return isSupportSmallGray(strategy, minAppVersion);
        }

        // 2.1 修改的是兜底策略-只有兜底策略的情况
        if (StringUtils.isBlank(versionBO.getVersions()) || versionBO.getVersions().replace("-", "").trim().isEmpty()) {
            return true;
        }

        // 2.1 修改的是兜底策略-有多个策略的情况
        List<String> allNoEmptyStrategy = massGraySupport.getAllNoEmptyStrategy(versionBO.getAppKey(), versionBO.getName(), versionBO.getVersion());
        for (String noEmptyStrategy : allNoEmptyStrategy) {
            if (isSupportSmallGray(noEmptyStrategy, minAppVersion)) {
                return false;
            }
        }

        return true;
    }

    /**
     * 策略条件是否支持小流量灰度
     *
     * @param strategy
     * @param minAppVersion
     * @return
     */
    private boolean isSupportSmallGray(String strategy, String minAppVersion) {
        if (StringUtils.isBlank(strategy)) {
            return false;
        }

        StrategyExpression strategyExpression = new StrategyExpression(strategy, StrategyExpression.Joiner.AND_WORD, TIGA_DIMENSIONS_MAP, ORANGE_IGNORE_DIMENSIONS_SET);

        for (OpExpr opExpr : strategyExpression.getParsedOpList()) {
            if (!opExpr.getKey().equals(Field.APP_VERSION.getValue())) {
                continue;
            }

            // 如果操作符包含小于则不支持
            OpExpr.Operator opr = opExpr.getOpr();
            if (OpExpr.Operator.LESS_EQUALS.equals(opr) || OpExpr.Operator.LESS.equals(opr)) {
                return false;
            }

            // 如果版本值小于最小版本则不支持
            String val = opExpr.getVal().toString();
            if (VersionUtil.versionCompare(val, minAppVersion) < 0) {
                return false;
            }
        }
        return true;
    }

    private LogicExpression fromStrategy(String strategy) {
        StrategyExpression strategyExpression = new StrategyExpression(strategy, StrategyExpression.Joiner.AND_WORD, TIGA_DIMENSIONS_MAP, ORANGE_IGNORE_DIMENSIONS_SET);

        List<LogicExpression> children = strategyExpression.getParsedOpList().stream().map(exp -> {
            JSONObject jsonObject = exp.toJSON("field", "op", "value");
            return ExpressionParser.fromCustomJson(JSON.toJSONString(jsonObject), null, TIGA_OP_MAP);
        }).collect(Collectors.toList());

        // 为空代表仅有不支持的表达式，如：did_hash
        return CollectionUtils.isEmpty(children) ? null :
                new LogicalExpressionNode(LogicalOperator.AND, children);
    }

    private List<String> parseUserList(String userStr) {
        if (StringUtils.isBlank(userStr)) {
            return Collections.emptyList();
        }

        return Arrays.stream(userStr.split(","))
                .filter(UserUtils::isEmpId)
                .map(UserUtils::completeEmpId)
                .collect(Collectors.toList());
    }

    @Data
    @Builder
    static class GrayPublishContent {
        private String appVersion;
        private String changeVersion;
        private int highLazy;
        private String loadLevel;
        private String md5;
        private String name;
        private String resourceId;
        private String type;
        private String version;
    }
}
